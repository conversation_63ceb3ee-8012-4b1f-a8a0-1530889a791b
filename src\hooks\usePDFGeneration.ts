import { useState } from 'react';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

interface StoryPage {
  text: string;
  sceneDescription?: string;
  imageUrl?: string;
}

interface StoryData {
  title: string;
  pages: StoryPage[];
  style?: string;
  mainCharacter?: string;
  ageGroup?: string;
}

export const usePDFGeneration = () => {
  const [isGenerating, setIsGenerating] = useState(false);

  const loadImage = (src: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = src;
    });
  };

  const generatePDF = async (storyData: StoryData): Promise<void> => {
    setIsGenerating(true);
    
    try {
      // Create PDF with A4 dimensions
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 20;
      const contentWidth = pageWidth - (margin * 2);
      
      // Add title page
      pdf.setFontSize(24);
      pdf.setFont('helvetica', 'bold');
      const titleLines = pdf.splitTextToSize(storyData.title, contentWidth);
      const titleHeight = titleLines.length * 10;
      const titleY = (pageHeight - titleHeight) / 2;
      
      pdf.text(titleLines, pageWidth / 2, titleY, { align: 'center' });
      
      // Add story metadata
      if (storyData.mainCharacter || storyData.ageGroup || storyData.style) {
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'normal');
        let metaY = titleY + titleHeight + 20;
        
        if (storyData.mainCharacter) {
          pdf.text(`Main Character: ${storyData.mainCharacter}`, pageWidth / 2, metaY, { align: 'center' });
          metaY += 8;
        }
        
        if (storyData.ageGroup) {
          pdf.text(`Age Group: ${storyData.ageGroup}`, pageWidth / 2, metaY, { align: 'center' });
          metaY += 8;
        }
        
        if (storyData.style) {
          pdf.text(`Illustration Style: ${storyData.style}`, pageWidth / 2, metaY, { align: 'center' });
        }
      }

      // Add story pages
      for (let i = 0; i < storyData.pages.length; i++) {
        const page = storyData.pages[i];
        
        // Add new page for each story page
        pdf.addPage();
        
        let currentY = margin;
        
        // Add page number
        pdf.setFontSize(10);
        pdf.setFont('helvetica', 'normal');
        pdf.text(`Page ${i + 1}`, pageWidth - margin, margin, { align: 'right' });
        currentY += 15;
        
        // Add image if available
        if (page.imageUrl && page.imageUrl !== '/placeholder.svg') {
          try {
            const img = await loadImage(page.imageUrl);
            
            // Calculate image dimensions to fit within content area
            const maxImageHeight = (pageHeight - margin * 2) * 0.6; // 60% of page height
            const aspectRatio = img.width / img.height;
            
            let imageWidth = contentWidth;
            let imageHeight = imageWidth / aspectRatio;
            
            if (imageHeight > maxImageHeight) {
              imageHeight = maxImageHeight;
              imageWidth = imageHeight * aspectRatio;
            }
            
            // Center the image
            const imageX = (pageWidth - imageWidth) / 2;
            
            pdf.addImage(img, 'JPEG', imageX, currentY, imageWidth, imageHeight);
            currentY += imageHeight + 15;
          } catch (error) {
            console.warn(`Failed to load image for page ${i + 1}:`, error);
            // Continue without image
          }
        }
        
        // Add text content
        pdf.setFontSize(14);
        pdf.setFont('helvetica', 'normal');
        
        const textLines = pdf.splitTextToSize(page.text, contentWidth);
        const remainingHeight = pageHeight - currentY - margin;
        const lineHeight = 8;
        const maxLines = Math.floor(remainingHeight / lineHeight);
        
        if (textLines.length <= maxLines) {
          pdf.text(textLines, margin, currentY);
        } else {
          // Split text across multiple pages if needed
          let lineIndex = 0;
          while (lineIndex < textLines.length) {
            const pageLinesCount = Math.min(maxLines, textLines.length - lineIndex);
            const pageLines = textLines.slice(lineIndex, lineIndex + pageLinesCount);
            
            pdf.text(pageLines, margin, currentY);
            lineIndex += pageLinesCount;
            
            if (lineIndex < textLines.length) {
              pdf.addPage();
              currentY = margin + 15; // Reset Y position for new page
              
              // Add page number for continuation page
              pdf.setFontSize(10);
              pdf.text(`Page ${i + 1} (continued)`, pageWidth - margin, margin, { align: 'right' });
              pdf.setFontSize(14);
            }
          }
        }
      }
      
      // Save the PDF
      const fileName = `${storyData.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_story.pdf`;
      pdf.save(fileName);
      
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw new Error('Failed to generate PDF');
    } finally {
      setIsGenerating(false);
    }
  };

  return {
    generatePDF,
    isGenerating
  };
};
