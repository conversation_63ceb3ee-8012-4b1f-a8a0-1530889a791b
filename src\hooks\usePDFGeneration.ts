import { useState } from 'react';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

// Convert Google Cloud Storage gs:// URLs to HTTPS URLs
const convertGcsUriToHttps = (gcsUri: string): string => {
  if (gcsUri.startsWith('gs://')) {
    // Convert gs://bucket-name/path to https://storage.cloud.google.com/bucket-name/path
    return gcsUri.replace('gs://', 'https://storage.cloud.google.com/');
  }
  return gcsUri;
};

interface StoryPage {
  text: string;
  sceneDescription?: string;
  imageUrl?: string;
}

interface StoryData {
  title: string;
  pages: StoryPage[];
  style?: string;
  mainCharacter?: string;
  ageGroup?: string;
}

export const usePDFGeneration = () => {
  const [isGenerating, setIsGenerating] = useState(false);

  const loadImage = (src: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      const img = new Image();

      img.onload = () => {
        console.log('Image loaded successfully:', src);
        resolve(img);
      };

      img.onerror = (error) => {
        console.error('Failed to load image with CORS:', src, error);

        // Try loading without CORS as fallback
        const fallbackImg = new Image();
        fallbackImg.onload = () => {
          console.log('Image loaded successfully without CORS:', src);
          resolve(fallbackImg);
        };
        fallbackImg.onerror = (fallbackError) => {
          console.error('Failed to load image without CORS:', src, fallbackError);
          reject(fallbackError);
        };
        fallbackImg.src = src;
      };

      // Add a timeout to prevent hanging
      setTimeout(() => {
        if (!img.complete) {
          console.warn('Image loading timeout:', src);
          reject(new Error('Image loading timeout'));
        }
      }, 15000); // 15 second timeout

      // Try with CORS first
      img.crossOrigin = 'anonymous';
      img.src = src;
    });
  };

  const generatePDF = async (storyData: StoryData): Promise<void> => {
    setIsGenerating(true);

    try {
      console.log('Starting PDF generation for story:', storyData.title);
      console.log('Story pages:', storyData.pages);

      // Create PDF with A4 landscape dimensions
      const pdf = new jsPDF('l', 'mm', 'a4'); // 'l' for landscape
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 20;
      const contentWidth = pageWidth - (margin * 2);

      console.log(`PDF dimensions: ${pageWidth}x${pageHeight}mm (landscape)`);
      console.log(`Content area: ${contentWidth}mm wide`);

      // Add title page
      pdf.setFontSize(24);
      pdf.setFont('helvetica', 'bold');
      const titleLines = pdf.splitTextToSize(storyData.title, contentWidth);
      const titleHeight = titleLines.length * 10;
      const titleY = (pageHeight - titleHeight) / 2;

      pdf.text(titleLines, pageWidth / 2, titleY, { align: 'center' });

      // Add story metadata
      if (storyData.mainCharacter || storyData.ageGroup || storyData.style) {
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'normal');
        let metaY = titleY + titleHeight + 20;

        if (storyData.mainCharacter) {
          pdf.text(`Main Character: ${storyData.mainCharacter}`, pageWidth / 2, metaY, { align: 'center' });
          metaY += 8;
        }

        if (storyData.ageGroup) {
          pdf.text(`Age Group: ${storyData.ageGroup}`, pageWidth / 2, metaY, { align: 'center' });
          metaY += 8;
        }

        if (storyData.style) {
          pdf.text(`Illustration Style: ${storyData.style}`, pageWidth / 2, metaY, { align: 'center' });
        }
      }

      // Add story pages
      for (let i = 0; i < storyData.pages.length; i++) {
        const page = storyData.pages[i];

        // Add new page for each story page
        pdf.addPage();

        let currentY = margin;

        // Add page number
        pdf.setFontSize(10);
        pdf.setFont('helvetica', 'normal');
        pdf.text(`Page ${i + 1}`, pageWidth - margin, margin, { align: 'right' });
        currentY += 15;

        // Add image if available (at the top of the page)
        if (page.imageUrl && page.imageUrl !== '/placeholder.svg') {
          try {
            console.log(`Loading image for page ${i + 1}:`, page.imageUrl);
            // Convert GCS URLs to HTTPS if needed
            const imageUrl = convertGcsUriToHttps(page.imageUrl);
            console.log(`Loading image for page ${i + 1}:`, page.imageUrl, '-> converted to:', imageUrl);
            const img = await loadImage(imageUrl);

            // Calculate image dimensions for landscape layout
            // Use more space for image in landscape mode
            const maxImageHeight = (pageHeight - margin * 2) * 0.65; // 65% of page height
            const maxImageWidth = contentWidth * 0.8; // 80% of page width
            const aspectRatio = img.width / img.height;

            let imageWidth = maxImageWidth;
            let imageHeight = imageWidth / aspectRatio;

            // If height is too large, scale by height instead
            if (imageHeight > maxImageHeight) {
              imageHeight = maxImageHeight;
              imageWidth = imageHeight * aspectRatio;
            }

            // Center the image horizontally
            const imageX = (pageWidth - imageWidth) / 2;

            // Determine image format
            let imageFormat = 'JPEG';
            if (imageUrl.toLowerCase().includes('.png')) {
              imageFormat = 'PNG';
            } else if (imageUrl.toLowerCase().includes('.webp')) {
              imageFormat = 'WEBP';
            }

            // Add the image
            pdf.addImage(img, imageFormat, imageX, currentY, imageWidth, imageHeight);
            currentY += imageHeight + 20; // More space between image and text

            console.log(`Image added successfully for page ${i + 1} (${imageFormat}, ${imageWidth}x${imageHeight}mm)`);
          } catch (error) {
            console.warn(`Failed to load image for page ${i + 1}:`, error);
            // Add a placeholder text when image fails
            pdf.setFontSize(12);
            pdf.setFont('helvetica', 'italic');
            pdf.text('[Image not available]', pageWidth / 2, currentY, { align: 'center' });
            currentY += 30;
            pdf.setFontSize(14);
            pdf.setFont('helvetica', 'normal');
          }
        }

        // Add text content below the image
        pdf.setFontSize(16); // Slightly larger text for landscape
        pdf.setFont('helvetica', 'normal');

        // Use more of the width for text in landscape mode
        const textWidth = contentWidth * 0.9;
        const textX = (pageWidth - textWidth) / 2; // Center the text

        const textLines = pdf.splitTextToSize(page.text, textWidth);
        const remainingHeight = pageHeight - currentY - margin;
        const lineHeight = 10; // Slightly more line height for readability
        const maxLines = Math.floor(remainingHeight / lineHeight);

        if (textLines.length <= maxLines) {
          pdf.text(textLines, textX, currentY);
        } else {
          // Split text across multiple pages if needed
          let lineIndex = 0;
          while (lineIndex < textLines.length) {
            const pageLinesCount = Math.min(maxLines, textLines.length - lineIndex);
            const pageLines = textLines.slice(lineIndex, lineIndex + pageLinesCount);

            pdf.text(pageLines, textX, currentY);
            lineIndex += pageLinesCount;

            if (lineIndex < textLines.length) {
              pdf.addPage();
              currentY = margin + 15; // Reset Y position for new page

              // Add page number for continuation page
              pdf.setFontSize(10);
              pdf.text(`Page ${i + 1} (continued)`, pageWidth - margin, margin, { align: 'right' });
              pdf.setFontSize(16);
            }
          }
        }
      }

      // Save the PDF
      const fileName = `${storyData.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_story.pdf`;
      pdf.save(fileName);

    } catch (error) {
      console.error('Error generating PDF:', error);
      throw new Error('Failed to generate PDF');
    } finally {
      setIsGenerating(false);
    }
  };

  return {
    generatePDF,
    isGenerating
  };
};
