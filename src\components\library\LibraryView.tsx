import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { PlusCircle, BookOpen, Calendar, Users, Image } from 'lucide-react';
import { motion } from 'framer-motion';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useImageStorage, type ImageMetadata } from '@/hooks/useImageStorage';
import type { Tables } from '@/integrations/supabase/types';

interface Story {
  id: string;
  title: string;
  main_character: string;
  created_at: string;
  pages: any[];
  style: string;
  age_group: string;
  setting?: string;
  plot_idea?: string;
}

interface LibraryViewProps {
  onCreateNew: () => void;
}

const LibraryView = ({ onCreateNew }: LibraryViewProps) => {
  const [stories, setStories] = useState<Story[]>([]);
  const [images, setImages] = useState<ImageMetadata[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const { getAllImages } = useImageStorage();

  const fetchStories = async () => {
    try {
      const { data, error } = await supabase
        .from('user_stories')
        .select('*')
        .eq('status', 'completed')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      // Cast the Supabase data to our Story interface
      const typedStories: Story[] = (data || []).map((story: Tables<'user_stories'>) => ({
        id: story.id,
        title: story.title,
        main_character: story.main_character,
        created_at: story.created_at,
        pages: Array.isArray(story.pages) ? story.pages : [],
        style: story.style || '',
        age_group: story.age_group || '',
        setting: story.setting || '',
        plot_idea: story.plot_idea || ''
      }));

      setStories(typedStories);
    } catch (error) {
      console.error('Error fetching stories:', error);
      toast({
        title: "Error",
        description: "Failed to load your stories. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchImages = async () => {
    try {
      const imageData = await getAllImages();
      setImages(imageData);
    } catch (error) {
      console.error('Error fetching images:', error);
      toast({
        title: "Error",
        description: "Failed to load images. Please try again.",
        variant: "destructive",
      });
    }
  };

  const fetchData = async () => {
    setIsLoading(true);
    await Promise.all([fetchStories(), fetchImages()]);
    setIsLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, []);

  const getStyleDisplayName = (style: string) => {
    const styleNames: Record<string, string> = {
      'watercolor': 'Watercolor Dreams',
      'cartoon': 'Playful Cartoon',
      'papercut': 'Paper Cutouts',
      'pencil': 'Pencil Sketch',
      '3d': '3D Wonderland',
      'collage': 'Mixed Media'
    };
    return styleNames[style] || style;
  };

  const getAgeGroupDisplay = (ageGroup: string) => {
    const ageGroups: Record<string, string> = {
      '2-4': 'Toddlers (2-4)',
      '5-7': 'Early Readers (5-7)',
      '8-10': 'Independent (8-10)'
    };
    return ageGroups[ageGroup] || ageGroup;
  };

  const getCoverImage = (story: Story) => {
    // First try to get image from story_images table (for the first page)
    const storyImages = images.filter(img => img.story_id === story.id);
    const firstPageImage = storyImages.find(img => img.page_number === 1);

    if (firstPageImage) {
      return firstPageImage.image_url;
    }

    // Fallback to pages field if available
    if (story.pages && story.pages.length > 0 && story.pages[0].imageUrl) {
      return story.pages[0].imageUrl;
    }

    return '/placeholder.svg';
  };



  const getStoredImages = () => {
    return images.map(img => ({
      url: img.image_url,
      storyTitle: (img as any).user_stories?.title || 'Unknown Story',
      storyId: img.story_id,
      pageIndex: img.page_number,
      id: img.id
    }));
  };

  if (isLoading) {
    return (
      <div className="container story-container">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-muted rounded-full"></div>
            <div className="w-16 h-16 border-t-4 border-blue animate-rotate-slow absolute top-0 rounded-full"></div>
          </div>
        </div>
      </div>
    );
  }

  const allStoredImages = getStoredImages();

  return (
    <div className="container story-container">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-slate-800">My Story Library</h1>
        <Button
          onClick={onCreateNew}
          className="bg-amber hover:bg-amber-dark text-primary-foreground"
        >
          <PlusCircle className="mr-2 h-4 w-4" /> Create New Story
        </Button>
      </div>

      <Tabs defaultValue="stories" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="stories" className="flex items-center space-x-2">
            <BookOpen className="h-4 w-4" />
            <span>Stories ({stories.length})</span>
          </TabsTrigger>
          <TabsTrigger value="images" className="flex items-center space-x-2">
            <Image className="h-4 w-4" />
            <span>Images ({allStoredImages.length})</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="stories" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {stories.map((story, index) => (
          <motion.div
            key={story.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-300 cursor-pointer">
              <div className="aspect-[4/3] bg-slate-100 relative">
                <img
                  src={getCoverImage(story)}
                  alt={story.title}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    console.error(`Image failed to load for story "${story.title}":`, e.currentTarget.src);
                    e.currentTarget.src = '/placeholder.svg';
                  }}
                />
              </div>
              <CardContent className="p-4">
                <h3 className="font-semibold text-lg mb-1 line-clamp-1">{story.title}</h3>
                <p className="text-sm text-slate-600 mb-2 line-clamp-1">
                  <Users className="inline w-3 h-3 mr-1" />
                  {story.main_character}
                </p>
                <div className="flex justify-between items-center text-sm text-slate-500 mb-3">
                  <span className="flex items-center">
                    <Calendar className="w-3 h-3 mr-1" />
                    {new Date(story.created_at).toLocaleDateString()}
                  </span>
                  <span className="flex items-center">
                    <BookOpen className="w-3 h-3 mr-1" />
                    {story.pages?.length || 0} pages
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2.5 py-1 rounded-full">
                    {getStyleDisplayName(story.style)}
                  </span>
                  <span className="inline-block bg-green-100 text-green-800 text-xs px-2.5 py-1 rounded-full">
                    {getAgeGroupDisplay(story.age_group)}
                  </span>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: stories.length * 0.1 }}
        >
          <Card
            className="overflow-hidden border-dashed border-2 border-slate-300 flex items-center justify-center h-full min-h-[200px] hover:border-amber hover:bg-amber/5 transition-colors cursor-pointer"
            onClick={onCreateNew}
          >
            <CardContent className="flex flex-col items-center justify-center text-center p-6">
              <PlusCircle className="h-12 w-12 text-slate-400 mb-4" />
              <h3 className="font-semibold text-lg mb-1">Create New Story</h3>
              <p className="text-sm text-slate-500">Start your storytelling journey</p>
            </CardContent>
          </Card>
        </motion.div>
      </div>

          {stories.length === 0 && (
            <div className="text-center py-16">
              <BookOpen className="h-16 w-16 text-slate-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-slate-600 mb-2">No stories yet</h3>
              <p className="text-slate-500 mb-6">Create your first AI-generated children's story!</p>
              <Button onClick={onCreateNew} className="bg-amber hover:bg-amber-dark">
                <PlusCircle className="mr-2 h-4 w-4" /> Create Your First Story
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="images" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {allStoredImages.map((image, index) => (
              <motion.div
                key={`${image.id || image.storyId}-${image.pageIndex}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                  <div className="aspect-square bg-slate-100 relative">
                    <img
                      src={image.url}
                      alt={`${image.storyTitle} - Page ${image.pageIndex}`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = '/placeholder.svg';
                      }}
                    />
                  </div>
                  <CardContent className="p-3">
                    <h4 className="font-medium text-sm mb-1 line-clamp-1">{image.storyTitle}</h4>
                    <p className="text-xs text-slate-500">Page {image.pageIndex}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {allStoredImages.length === 0 && (
            <div className="text-center py-16">
              <Image className="h-16 w-16 text-slate-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-slate-600 mb-2">No images yet</h3>
              <p className="text-slate-500 mb-6">Create stories to generate beautiful illustrations!</p>
              <Button onClick={onCreateNew} className="bg-amber hover:bg-amber-dark">
                <PlusCircle className="mr-2 h-4 w-4" /> Create Your First Story
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default LibraryView;
